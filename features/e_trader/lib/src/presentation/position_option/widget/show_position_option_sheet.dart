import 'dart:async';
import 'package:duplo/duplo.dart';
import 'package:e_trader/src/data/api/filtered_position_type.dart';
import 'package:e_trader/src/data/socket/position_model.dart';
import 'package:e_trader/src/di/di_container.dart';
import 'package:e_trader/src/domain/model/grouped_positions.dart';
import 'package:e_trader/src/domain/model/trade_type.dart';
import 'package:e_trader/src/presentation/model/symbol_detail_view_model.dart';
import 'package:e_trader/src/presentation/position_option/bloc/position_option_bloc.dart';
import 'package:e_trader/src/presentation/positions_and_trades/position_header.dart';
import 'package:e_trader/src/presentation/positions_and_trades/trade_tile.dart';
import 'package:equiti_localization/equiti_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:prelude/prelude.dart';
import 'package:e_trader/src/assets/assets.gen.dart' as trader;
part 'close_dialog_content_widget.dart';

FutureOr<void> showPositionOptionSheet(
  BuildContext context,
  SymbolDetailViewModel symbolDetailViewModel,
  String? currency,
) {
  var localization = EquitiLocalization.of(context);
  final theme = context.duploTheme;
  final style = context.duploTextStyles;

  DuploSheet.showModalSheetV2<void>(
    context,
    appBar: DuploAppBar(
      automaticallyImplyLeading: false,
      title: "",
      duploAppBarTextAlign: DuploAppBarTextAlign.left,
      titleWidget: DuploText(
        text: localization.trader_positions,
        style: style.textLg,
        fontWeight: DuploFontWeight.bold,
      ),
      actions: [
        IconButton(
          icon: Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    ),
    builder:
        (child) => BlocProvider(
          create:
              (_) =>
                  diContainer<PositionOptionBloc>()..add(
                    PositionOptionEvent.onSubscribeToGroupedPositions(
                      symbolDetailViewModel.symbolName,
                    ),
                  ),
          child: child,
        ),
    content: SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlocBuilder<PositionOptionBloc, PositionOptionState>(
            buildWhen: (previous, current) => previous != current,
            builder: (blocContext, state) {
              return switch (state.processState) {
                PositionOptionLoadingProcessState() => DuploShimmerListItem(
                  height: 40,
                ),
                PositionOptionSuccessProcessState() => () {
                  final groupedPosition = state.groupedPositions;
                  return groupedPosition != null &&
                          groupedPosition.positions.isNotEmpty
                      ? PositionHeader(
                        productName: groupedPosition.symbol,
                        profit: groupedPosition.totalProfit,
                        margin: groupedPosition.totalMargin,
                        productIcon: groupedPosition.url,
                        isHedging: groupedPosition.isHedged,
                        tradeType: groupedPosition.groupTradeType,
                        lots: groupedPosition.totalLotSize,
                        currency: currency,
                      )
                      : const SizedBox.shrink();
                }(),
                _ => DuploShimmerListItem(height: 40),
              };
            },
          ),
          Container(
            color: theme.background.bgSecondary,
            padding: EdgeInsetsDirectional.only(start: 10, end: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 12),
                TextChevronWidget(
                  title: localization.trader_marketDetails,
                  backgroundColor: theme.background.bgSecondary,
                  onPressed: () {
                    diContainer<PositionOptionBloc>()..add(
                      PositionOptionEvent.navigateToProductDetails(
                        symbolDetail: symbolDetailViewModel,
                      ),
                    );
                  },
                ),
                Divider(
                  height: 0,
                  color: theme.border.borderSecondary,
                  endIndent: 10,
                  indent: 10,
                ),
                TextChevronWidget(
                  title: localization.trader_viewChart,
                  backgroundColor: theme.background.bgSecondary,
                  onPressed: () {
                    //TODO: show chart
                  },
                ),
                Divider(
                  height: 0,
                  color: theme.border.borderSecondary,
                  endIndent: 10,
                  indent: 10,
                ),
                SizedBox(height: 24),
                Padding(
                  padding: const EdgeInsets.fromLTRB(12, 0, 0, 0),
                  child: DuploText(
                    text: localization.trader_closePositionBy,
                    fontWeight: DuploFontWeight.semiBold,
                    style: context.duploTextStyles.textLg,
                    color: theme.text.textPrimary,
                  ),
                ),
              ],
            ),
          ),
          BlocConsumer<PositionOptionBloc, PositionOptionState>(
            listenWhen:
                (previous, current) =>
                    previous.processState != current.processState,
            listener: (listenerContext, state) {
              final processState = state.processState;
              if (processState
                  is PositionOptionClosePositionsSuccessProcessState) {
                final toast = DuploToast();
                toast.showToastMessage(
                  context: context,
                  widget: DuploToastTrade(
                    isPriceCentered: true,
                    priceColor:
                        processState.price > 0
                            ? theme.text.textSuccessPrimary
                            : theme.text.textErrorPrimary,
                    onLeadingAction: toast.hidesToastMessage,
                    titleMessage: processState.successMessageTitle,
                    trade: TradeToastModel(
                      symbolImage: symbolDetailViewModel.imageURL ?? '',
                      symbolName: symbolDetailViewModel.symbolName,
                      lotSize: EquitiFormatter.formatDynamicDigits(
                        value: processState.lotSize,
                        digits: processState.digits,
                        locale: Localizations.localeOf(context).toString(),
                      ),
                      price: EquitiFormatter.decimalPatternDigits(
                        value: processState.price,
                        digits: 2,
                        locale: Localizations.localeOf(context).toString(),
                      ),
                      type: switch (processState.tradeType) {
                        TradeType.buy => TradeToastType.buy,
                        TradeType.sell => TradeToastType.sell,
                        null => TradeToastType.buySellCombined,
                      },
                      currency: state.accountCurrency,
                    ),
                    type: ToastMessageType.success,
                    actionButtonTitle: localization.trader_viewPortfolio,
                  ),
                );
                if (state.groupedPositions?.positions case final positions
                    when positions!.isEmpty) {
                  Navigator.of(listenerContext).pop();
                }
              } else if (state.processState
                  is PositionOptionClosePositionsErrorProcessState) {
                final toast = DuploToast();

                toast.showToastMessage(
                  context: context,
                  widget: DuploToastMessage(
                    titleMessage: localization.trader_issueClosingTrades,

                    descriptionMessage:
                        EquitiLocalization.of(
                          listenerContext,
                        ).trader_loadingErrorDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast.hidesToastMessage(),
                  ),
                );
              } else if (state.processState
                  is PositionOptionMarketClosedProcessState) {
                final toast = DuploToast();
                toast.showToastMessage(
                  context: context,
                  widget: DuploToastMessage(
                    titleMessage: localization.trader_marketIsClosed,
                    descriptionMessage:
                        localization
                            .trader_closeAllTrades_marketIsClosedDescription,
                    messageType: ToastMessageType.error,
                    onLeadingAction: () => toast.hidesToastMessage(),
                  ),
                );
              }
            },
            buildWhen:
                (previous, current) =>
                    previous.groupedPositions != current.groupedPositions,
            builder: (buildContext, state) {
              return switch (state.processState) {
                PositionOptionLoadingProcessState() => DuploShimmerList(
                  hasLeading: false,
                  itemCount: 5,
                  height: 24,
                ),

                _ => Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsetsDirectional.only(start: 10, end: 8),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (state.buyTrades case final buyTrades
                              when buyTrades.isNotEmpty)
                            Column(
                              children: [
                                TextChevronWidget(
                                  title: localization.trader_allBuyTrades,
                                  backgroundColor: theme.background.bgSecondary,
                                  onPressed: () {
                                    if (state.shouldHideClosePositionsDialog) {
                                      buildContext
                                          .read<PositionOptionBloc>()
                                          .add(
                                            PositionOptionEvent.closeBuyTrades(
                                              localization
                                                  .trader_closedAllBuyTrades,
                                            ),
                                          );
                                    } else {
                                      _showCloseTradeBottomSheet(
                                        buildContext,
                                        buyTrades,
                                        localization.trader_allBuyTrades,
                                        () => buildContext
                                            .read<PositionOptionBloc>()
                                            .add(
                                              PositionOptionEvent.closeBuyTrades(
                                                localization
                                                    .trader_closedAllBuyTrades,
                                              ),
                                            ),
                                        FilteredPositionType.buy,
                                      );
                                    }
                                  },
                                ),
                                Divider(
                                  height: 0,
                                  color: theme.border.borderSecondary,
                                  endIndent: 10,
                                  indent: 10,
                                ),
                              ],
                            ),
                          if (state.sellTrades case final sellTrades
                              when sellTrades.isNotEmpty)
                            Column(
                              children: [
                                TextChevronWidget(
                                  backgroundColor: theme.background.bgSecondary,
                                  title: localization.trader_allSellTrades,
                                  onPressed: () {
                                    if (state.shouldHideClosePositionsDialog) {
                                      buildContext
                                          .read<PositionOptionBloc>()
                                          .add(
                                            PositionOptionEvent.closeSellTrades(
                                              localization
                                                  .trader_closedAllSellTrades,
                                            ),
                                          );
                                    } else {
                                      _showCloseTradeBottomSheet(
                                        buildContext,
                                        sellTrades,
                                        localization.trader_allSellTrades,
                                        () => buildContext
                                            .read<PositionOptionBloc>()
                                            .add(
                                              PositionOptionEvent.closeSellTrades(
                                                localization
                                                    .trader_closedAllSellTrades,
                                              ),
                                            ),
                                        FilteredPositionType.sell,
                                      );
                                    }
                                  },
                                ),
                                Divider(
                                  height: 0,
                                  color: theme.border.borderSecondary,
                                  endIndent: 10,
                                  indent: 10,
                                ),
                              ],
                            ),

                          if (state.losingTrades case final losingTrades
                              when losingTrades.isNotEmpty)
                            Column(
                              children: [
                                TextChevronWidget(
                                  title: localization.trader_allLosingTrades,
                                  backgroundColor: theme.background.bgSecondary,
                                  onPressed: () {
                                    if (state.shouldHideClosePositionsDialog) {
                                      buildContext.read<PositionOptionBloc>().add(
                                        PositionOptionEvent.closeLosingTrades(
                                          localization
                                              .trader_closedAllLosingTrades,
                                        ),
                                      );
                                    } else {
                                      _showCloseTradeBottomSheet(
                                        buildContext,
                                        losingTrades,
                                        localization.trader_allLosingTrades,
                                        () => buildContext
                                            .read<PositionOptionBloc>()
                                            .add(
                                              PositionOptionEvent.closeLosingTrades(
                                                localization
                                                    .trader_closedAllLosingTrades,
                                              ),
                                            ),
                                        FilteredPositionType.losing,
                                      );
                                    }
                                  },
                                ),
                                Divider(
                                  height: 0,
                                  color: theme.border.borderSecondary,
                                  endIndent: 10,
                                  indent: 10,
                                ),
                              ],
                            ),
                          if (state.winningTrades case final winningTrades
                              when winningTrades.isNotEmpty)
                            Column(
                              children: [
                                TextChevronWidget(
                                  title: localization.trader_allWinningTrades,
                                  backgroundColor: theme.background.bgSecondary,
                                  onPressed: () {
                                    if (state.shouldHideClosePositionsDialog) {
                                      buildContext.read<PositionOptionBloc>().add(
                                        PositionOptionEvent.closeWinningTrades(
                                          localization
                                              .trader_closedAllWinningTrades,
                                        ),
                                      );
                                    } else {
                                      _showCloseTradeBottomSheet(
                                        buildContext,
                                        winningTrades,
                                        localization.trader_allWinningTrades,
                                        () => buildContext
                                            .read<PositionOptionBloc>()
                                            .add(
                                              PositionOptionEvent.closeWinningTrades(
                                                localization
                                                    .trader_closedAllWinningTrades,
                                              ),
                                            ),
                                        FilteredPositionType.winning,
                                      );
                                    }
                                  },
                                ),
                                Divider(
                                  height: 0,
                                  color: theme.border.borderSecondary,
                                  endIndent: 10,
                                  indent: 10,
                                ),
                              ],
                            ),
                          if (state.groupedPositions?.positions ?? []
                              case final positions when positions.isNotEmpty)
                            TextChevronWidget(
                              title: localization.trader_allTrades,
                              backgroundColor: theme.background.bgSecondary,
                              onPressed: () {
                                if (state.shouldHideClosePositionsDialog) {
                                  buildContext.read<PositionOptionBloc>().add(
                                    PositionOptionEvent.closeAllTrades(
                                      localization.trader_closedAllTrades,
                                    ),
                                  );
                                } else {
                                  _showCloseTradeBottomSheet(
                                    buildContext,
                                    positions,
                                    localization.trader_allTrades,
                                    () => buildContext
                                        .read<PositionOptionBloc>()
                                        .add(
                                          PositionOptionEvent.closeAllTrades(
                                            localization.trader_closedAllTrades,
                                          ),
                                        ),
                                    FilteredPositionType.all,
                                  );
                                }
                              },
                            ),

                          const SizedBox(height: 16),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(12, 0, 0, 0),
                            child: DuploText(
                              text:
                                  "${EquitiFormatter.formatNumber(value: state.groupedPositions?.positions.length ?? 0, locale: Localizations.localeOf(context).toString())} ${localization.trader_open_trades}",
                              fontWeight: DuploFontWeight.semiBold,
                              style: context.duploTextStyles.textLg,
                              color: theme.text.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Column(
                      children: [
                        ...?state.groupedPositions?.positions.map((position) {
                          return TradeTile(
                            digits: position.digits,
                            lots: position.lotSize,
                            tradeType: position.positionType,
                            profit: position.profit ?? 0.0,
                            priceChange:
                                position.positionType == TradeType.buy
                                    ? position.buyPercentage
                                    : position.sellPercentage,
                            currentPrice: position.currentPrice,

                            tpValue: position.takeProfit,
                            slValue: position.stopLoss,
                            productName: state.groupedPositions!.symbol,
                            productIcon: state.groupedPositions!.url,
                            currency: state.accountCurrency,
                          );
                        }).toList(),
                        const SizedBox(height: 50),
                      ],
                    ),
                  ],
                ),
              };
            },
          ),
        ],
      ),
    ),
  );
}

void _showCloseTradeBottomSheet(
  BuildContext context,
  List<PositionModel> positions,
  String subTitle,
  VoidCallback onPressed,
  FilteredPositionType type,
) {
  DuploSheet.showModalSheetV2<void>(
    context,
    appBar: DuploAppBar(
      automaticallyImplyLeading: false,
      title: "",
      titleWidget: Align(
        alignment: Alignment.centerLeft,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DuploText(
              text: EquitiLocalization.of(context).trader_close_title,
              style: context.duploTextStyles.textLg,
              fontWeight: DuploFontWeight.bold,
            ),
            DuploText(
              text: subTitle,
              style: context.duploTextStyles.textXs,
              color: context.duploTheme.text.textTertiary,
            ),
          ],
        ),
      ),
      actions: [
        IconButton(
          icon: Assets.images.closeIc.svg(),
          onPressed: () => Navigator.pop(context),
        ),
      ],
    ),
    content: _CloseDialogContentWidget(
      subTitle: subTitle,
      positions: positions,
      positionOptionBloc: context.read<PositionOptionBloc>(),
      onPressed: onPressed,
      parentContext: context,
      type: type,
      currency: context.read<PositionOptionBloc>().state.accountCurrency,
    ),
  );
}

double? _getFilteredPositionsProfit(
  PositionOptionState state,
  FilteredPositionType type,
) {
  switch (type) {
    case FilteredPositionType.buy:
      return GroupedPositions(
        url: state.buyTrades.firstOrNull?.productLogoUrl ?? '',
        symbol: state.buyTrades.firstOrNull?.symbol ?? "",
        positions: state.buyTrades,
      ).totalProfit;
    case FilteredPositionType.sell:
      return GroupedPositions(
        url: state.sellTrades.firstOrNull?.productLogoUrl ?? '',
        symbol: state.sellTrades.firstOrNull?.symbol ?? "",
        positions: state.sellTrades,
      ).totalProfit;
    case FilteredPositionType.losing:
      return GroupedPositions(
        url: state.losingTrades.firstOrNull?.productLogoUrl ?? '',
        symbol: state.losingTrades.firstOrNull?.symbol ?? "",
        positions: state.losingTrades,
      ).totalProfit;
    case FilteredPositionType.winning:
      return GroupedPositions(
        url: state.winningTrades.firstOrNull?.productLogoUrl ?? '',
        symbol: state.winningTrades.firstOrNull?.symbol ?? "",
        positions: state.winningTrades,
      ).totalProfit;

    case FilteredPositionType.all:
      return state.groupedPositions?.totalProfit;
  }
}
